'use client'

import { useState } from 'react'
import { MaintenanceInvoice } from '@/lib/supabase'
import { InvoiceWithUnit } from '@/lib/services/invoiceService'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import { Modal, ModalBody, ModalFooter, ModalHeader } from '@/components/ui/Modal'
import { formatCurrency, formatDateShort, getMonthName } from '@/lib/utils'
import {
  PencilIcon,
  TrashIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  QueueListIcon,
} from '@heroicons/react/24/outline'

interface InvoicesListProps {
  invoices: InvoiceWithUnit[]
  onEdit: (invoice: MaintenanceInvoice) => void
  onCreate: () => void
  onBulkCreate: () => void
  onDelete: (invoice: MaintenanceInvoice) => void
  onSearch: (query: string) => void
  onStatusFilter: (status: string) => void
  isLoading?: boolean
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-green-100 text-green-800',
  overdue: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800',
}

export function InvoicesList({
  invoices,
  onEdit,
  onCreate,
  onBulkCreate,
  onDelete,
  onSearch,
  onStatusFilter,
  isLoading = false
}: InvoicesListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; invoice: MaintenanceInvoice | null }>({
    isOpen: false,
    invoice: null
  })

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch(query)
  }

  const handleStatusFilter = (status: string) => {
    setSelectedStatus(status)
    onStatusFilter(status)
  }

  const handleDeleteClick = (invoice: MaintenanceInvoice) => {
    setDeleteModal({ isOpen: true, invoice })
  }

  const handleDeleteConfirm = () => {
    if (deleteModal.invoice) {
      onDelete(deleteModal.invoice)
      setDeleteModal({ isOpen: false, invoice: null })
    }
  }

  const handleDeleteCancel = () => {
    setDeleteModal({ isOpen: false, invoice: null })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
          <p className="text-gray-600">Manage maintenance invoices and billing</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={onBulkCreate} variant="outline" className="flex items-center">
            <QueueListIcon className="h-4 w-4 mr-2" />
            Bulk Generate
          </Button>
          <Button onClick={onCreate} className="flex items-center">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Invoice
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search invoices by number or description..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedStatus}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invoices ({invoices.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 ? (
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first invoice.
              </p>
              <div className="mt-6 flex justify-center space-x-3">
                <Button onClick={onCreate}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Invoice
                </Button>
                <Button onClick={onBulkCreate} variant="outline">
                  <QueueListIcon className="h-4 w-4 mr-2" />
                  Bulk Generate
                </Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice #</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Property</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      {invoice.invoice_number || `INV-${invoice.id.slice(-6)}`}
                    </TableCell>
                    <TableCell>{invoice.unit?.unit_number}</TableCell>
                    <TableCell>{invoice.unit?.property?.name}</TableCell>
                    <TableCell>
                      {getMonthName(invoice.month)} {invoice.year}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(invoice.amount)}
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[invoice.status as keyof typeof statusColors]}`}>
                        {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                      </span>
                    </TableCell>
                    <TableCell>
                      {invoice.due_date ? formatDateShort(invoice.due_date) : '-'}
                    </TableCell>
                    <TableCell>{formatDateShort(invoice.created_at)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(invoice)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteClick(invoice)}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        title="Delete Invoice"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            Are you sure you want to delete invoice "{deleteModal.invoice?.invoice_number}"? 
            This action cannot be undone and will also delete any associated payment records.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button variant="outline" onClick={handleDeleteCancel}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDeleteConfirm}>
            Delete Invoice
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  )
}
