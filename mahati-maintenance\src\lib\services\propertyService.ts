import { supabase } from '@/lib/supabase'
import { Property } from '@/lib/supabase'

export interface CreatePropertyData {
  name: string
  address: string
}

export interface UpdatePropertyData {
  name?: string
  address?: string
}

export class PropertyService {
  static async getAll(): Promise<Property[]> {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch properties: ${error.message}`)
    }

    return data || []
  }

  static async getById(id: string): Promise<Property | null> {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Property not found
      }
      throw new Error(`Failed to fetch property: ${error.message}`)
    }

    return data
  }

  static async create(propertyData: CreatePropertyData): Promise<Property> {
    const { data, error } = await supabase
      .from('properties')
      .insert([propertyData])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create property: ${error.message}`)
    }

    return data
  }

  static async update(id: string, propertyData: UpdatePropertyData): Promise<Property> {
    const { data, error } = await supabase
      .from('properties')
      .update(propertyData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update property: ${error.message}`)
    }

    return data
  }

  static async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('properties')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete property: ${error.message}`)
    }
  }

  static async search(query: string): Promise<Property[]> {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .or(`name.ilike.%${query}%,address.ilike.%${query}%`)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to search properties: ${error.message}`)
    }

    return data || []
  }

  static async getWithUnitsCount(): Promise<(Property & { units_count: number })[]> {
    const { data, error } = await supabase
      .from('properties')
      .select(`
        *,
        units:units(count)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch properties with units count: ${error.message}`)
    }

    return (data || []).map(property => ({
      ...property,
      units_count: property.units?.[0]?.count || 0
    }))
  }
}
