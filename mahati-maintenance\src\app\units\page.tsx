'use client'

import { useState, useEffect } from 'react'
import { Unit, Property } from '@/lib/supabase'
import { UnitService, CreateUnitData, UnitWithProperty } from '@/lib/services/unitService'
import { PropertyService } from '@/lib/services/propertyService'
import { MainLayout } from '@/components/layout/MainLayout'
import { UnitsList } from '@/components/units/UnitsList'
import { UnitForm } from '@/components/units/UnitForm'
import { Modal, ModalBody, ModalHeader } from '@/components/ui/Modal'
import { toast } from 'react-hot-toast'

export default function UnitsPage() {
  const [units, setUnits] = useState<UnitWithProperty[]>([])
  const [filteredUnits, setFilteredUnits] = useState<UnitWithProperty[]>([])
  const [properties, setProperties] = useState<Property[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [modal, setModal] = useState<{
    isOpen: boolean
    type: 'create' | 'edit'
    unit?: Unit
  }>({
    isOpen: false,
    type: 'create'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [unitsData, propertiesData] = await Promise.all([
        UnitService.getAll(),
        PropertyService.getAll()
      ])
      setUnits(unitsData)
      setFilteredUnits(unitsData)
      setProperties(propertiesData)
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('Failed to load units and properties')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreate = () => {
    setModal({ isOpen: true, type: 'create' })
  }

  const handleEdit = (unit: Unit) => {
    setModal({ isOpen: true, type: 'edit', unit })
  }

  const handleDelete = async (unit: Unit) => {
    try {
      await UnitService.delete(unit.id)
      toast.success('Unit deleted successfully')
      loadData()
    } catch (error) {
      console.error('Failed to delete unit:', error)
      toast.error('Failed to delete unit')
    }
  }

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setFilteredUnits(units)
      return
    }

    try {
      const searchResults = await UnitService.search(query)
      setFilteredUnits(searchResults)
    } catch (error) {
      console.error('Failed to search units:', error)
      toast.error('Failed to search units')
    }
  }

  const handlePropertyFilter = (propertyId: string) => {
    if (!propertyId) {
      setFilteredUnits(units)
      return
    }

    const filtered = units.filter(unit => unit.property_id === propertyId)
    setFilteredUnits(filtered)
  }

  const handleFormSubmit = async (data: CreateUnitData) => {
    try {
      if (modal.type === 'create') {
        await UnitService.create(data)
        toast.success('Unit created successfully')
      } else if (modal.unit) {
        await UnitService.update(modal.unit.id, data)
        toast.success('Unit updated successfully')
      }
      
      setModal({ isOpen: false, type: 'create' })
      loadData()
    } catch (error) {
      console.error('Failed to save unit:', error)
      toast.error('Failed to save unit')
      throw error // Re-throw to prevent modal from closing
    }
  }

  const handleModalClose = () => {
    setModal({ isOpen: false, type: 'create' })
  }

  return (
    <MainLayout>
      <UnitsList
        units={filteredUnits}
        properties={properties}
        onEdit={handleEdit}
        onCreate={handleCreate}
        onDelete={handleDelete}
        onSearch={handleSearch}
        onPropertyFilter={handlePropertyFilter}
        isLoading={isLoading}
      />

      {/* Unit Form Modal */}
      <Modal
        isOpen={modal.isOpen}
        onClose={handleModalClose}
        title={modal.type === 'create' ? 'Create Unit' : 'Edit Unit'}
        size="lg"
      >
        <ModalBody>
          <UnitForm
            unit={modal.unit}
            properties={properties}
            onSubmit={handleFormSubmit}
            onCancel={handleModalClose}
          />
        </ModalBody>
      </Modal>
    </MainLayout>
  )
}
