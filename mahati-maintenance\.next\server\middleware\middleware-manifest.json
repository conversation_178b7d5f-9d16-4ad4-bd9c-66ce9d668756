{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_7b58270d._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ae544a93.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Uv1tElnPc8Ruqs6IsvYgF0HX75XkTt4BJZySQxY2/4k=", "__NEXT_PREVIEW_MODE_ID": "4d9b629e00ad70d975f3095adc24e565", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "33ead0ff6caf4ead86f0670983fc96b65fb61a3a56c47b48ffb47755ae4a6bca", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9cf0e0dea50e808248a4b20a455d135cc58e390557895b669aa284620f3ec0df"}}}, "instrumentation": null, "functions": {}}