{"name": "mahati-maintenance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}