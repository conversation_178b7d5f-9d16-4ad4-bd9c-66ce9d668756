import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types (will be expanded as we develop)
export interface Property {
  id: string
  name: string
  address: string
  created_at: string
}

export interface Unit {
  id: string
  property_id: string
  unit_number: string
  floor?: number
  area?: number
  owner_name?: string
  owner_email?: string
  owner_phone?: string
  created_at: string
  // Joined data
  property?: Property
}

export interface MaintenanceInvoice {
  id: string
  unit_id: string
  amount: number
  month: number
  year: number
  status: 'pending' | 'paid' | 'overdue' | 'cancelled'
  due_date?: string
  invoice_number?: string
  description?: string
  created_at: string
  // Joined data
  unit?: Unit
}

export interface Payment {
  id: string
  invoice_id: string
  amount: number
  payment_date: string
  payment_method?: string
  notes?: string
  created_at: string
}
