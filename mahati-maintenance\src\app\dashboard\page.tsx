'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { MainLayout } from '@/components/layout/MainLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { PropertyService } from '@/lib/services/propertyService'
import { UnitService } from '@/lib/services/unitService'
import { InvoiceService } from '@/lib/services/invoiceService'
import { formatCurrency } from '@/lib/utils'
import {
  BuildingOfficeIcon,
  BuildingOffice2Icon,
  DocumentTextIcon,
  CreditCardIcon,
  ChartBarIcon,
  UsersIcon,
} from '@heroicons/react/24/outline'

interface DashboardStats {
  totalProperties: number
  totalUnits: number
  pendingInvoices: number
  outstandingAmount: number
}

export default function DashboardPage() {
  const { user, loading } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalProperties: 0,
    totalUnits: 0,
    pendingInvoices: 0,
    outstandingAmount: 0
  })
  const [isLoadingStats, setIsLoadingStats] = useState(true)

  useEffect(() => {
    loadDashboardStats()
  }, [])

  const loadDashboardStats = async () => {
    try {
      setIsLoadingStats(true)
      const [properties, units, invoiceStats] = await Promise.all([
        PropertyService.getAll(),
        UnitService.getAll(),
        InvoiceService.getInvoiceStats()
      ])

      setStats({
        totalProperties: properties.length,
        totalUnits: units.length,
        pendingInvoices: invoiceStats.pending + invoiceStats.overdue,
        outstandingAmount: invoiceStats.pendingAmount
      })
    } catch (error) {
      console.error('Failed to load dashboard stats:', error)
    } finally {
      setIsLoadingStats(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  const dashboardStats = [
    {
      name: 'Total Properties',
      value: isLoadingStats ? '...' : stats.totalProperties.toString(),
      icon: BuildingOfficeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      href: '/properties'
    },
    {
      name: 'Total Units',
      value: isLoadingStats ? '...' : stats.totalUnits.toString(),
      icon: BuildingOffice2Icon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      href: '/units'
    },
    {
      name: 'Pending Invoices',
      value: isLoadingStats ? '...' : stats.pendingInvoices.toString(),
      icon: DocumentTextIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      href: '/invoices'
    },
    {
      name: 'Outstanding Amount',
      value: isLoadingStats ? '...' : formatCurrency(stats.outstandingAmount),
      icon: CreditCardIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      href: '/payments'
    },
  ]

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome back, {user?.email?.split('@')[0]}!
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your properties today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {dashboardStats.map((stat) => (
            <Link key={stat.name} href={stat.href}>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                      <stat.icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Link href="/properties" className="block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center">
                    <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="font-medium">Add New Property</span>
                  </div>
                </Link>
                <Link href="/invoices" className="block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center">
                    <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="font-medium">Generate Invoices</span>
                  </div>
                </Link>
                <Link href="/reports" className="block w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center">
                    <ChartBarIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="font-medium">View Reports</span>
                  </div>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Activity will appear here as you use the system.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
