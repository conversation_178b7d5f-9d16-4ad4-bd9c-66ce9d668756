'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Unit, Property } from '@/lib/supabase'
import { validateEmail, validatePhone } from '@/lib/utils'

const unitSchema = z.object({
  property_id: z.string().min(1, 'Property is required'),
  unit_number: z.string().min(1, 'Unit number is required').max(20, 'Unit number must be less than 20 characters'),
  floor: z.number().min(0, 'Floor must be 0 or greater').optional().or(z.literal('')),
  area: z.number().min(1, 'Area must be greater than 0').optional().or(z.literal('')),
  owner_name: z.string().max(100, 'Owner name must be less than 100 characters').optional().or(z.literal('')),
  owner_email: z.string().refine((email) => !email || validateEmail(email), 'Invalid email format').optional().or(z.literal('')),
  owner_phone: z.string().refine((phone) => !phone || validatePhone(phone), 'Invalid phone format').optional().or(z.literal('')),
})

type UnitFormData = z.infer<typeof unitSchema>

interface UnitFormProps {
  unit?: Unit
  properties: Property[]
  onSubmit: (data: UnitFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function UnitForm({ unit, properties, onSubmit, onCancel, isLoading = false }: UnitFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<UnitFormData>({
    resolver: zodResolver(unitSchema),
    defaultValues: {
      property_id: unit?.property_id || '',
      unit_number: unit?.unit_number || '',
      floor: unit?.floor || '',
      area: unit?.area || '',
      owner_name: unit?.owner_name || '',
      owner_email: unit?.owner_email || '',
      owner_phone: unit?.owner_phone || '',
    },
  })

  const handleFormSubmit = async (data: UnitFormData) => {
    try {
      // Convert empty strings to undefined for optional fields
      const processedData = {
        ...data,
        floor: data.floor === '' ? undefined : Number(data.floor),
        area: data.area === '' ? undefined : Number(data.area),
        owner_name: data.owner_name === '' ? undefined : data.owner_name,
        owner_email: data.owner_email === '' ? undefined : data.owner_email,
        owner_phone: data.owner_phone === '' ? undefined : data.owner_phone,
      }
      await onSubmit(processedData)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label htmlFor="property_id" className="block text-sm font-medium text-gray-700 mb-2">
            Property <span className="text-red-500">*</span>
          </label>
          <select
            id="property_id"
            {...register('property_id')}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            disabled={!!unit} // Disable property change when editing
          >
            <option value="">Select a property</option>
            {properties.map((property) => (
              <option key={property.id} value={property.id}>
                {property.name}
              </option>
            ))}
          </select>
          {errors.property_id && (
            <p className="mt-1 text-sm text-red-600">{errors.property_id.message}</p>
          )}
        </div>

        <Input
          label="Unit Number"
          {...register('unit_number')}
          error={errors.unit_number?.message}
          placeholder="e.g., 101, A-1, 2B"
          required
        />

        <Input
          label="Floor"
          type="number"
          {...register('floor', { valueAsNumber: true })}
          error={errors.floor?.message}
          placeholder="e.g., 1, 2, 3"
          min="0"
        />

        <Input
          label="Area (sq ft)"
          type="number"
          {...register('area', { valueAsNumber: true })}
          error={errors.area?.message}
          placeholder="e.g., 1200, 850"
          min="1"
        />

        <div className="md:col-span-2">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Owner Information</h3>
        </div>

        <Input
          label="Owner Name"
          {...register('owner_name')}
          error={errors.owner_name?.message}
          placeholder="Full name of the owner"
        />

        <Input
          label="Owner Email"
          type="email"
          {...register('owner_email')}
          error={errors.owner_email?.message}
          placeholder="<EMAIL>"
        />

        <Input
          label="Owner Phone"
          type="tel"
          {...register('owner_phone')}
          error={errors.owner_phone?.message}
          placeholder="+91 9876543210"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={isSubmitting || isLoading}
          disabled={isSubmitting || isLoading}
        >
          {unit ? 'Update Unit' : 'Create Unit'}
        </Button>
      </div>
    </form>
  )
}
