'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { MaintenanceInvoice, Unit, Property } from '@/lib/supabase'
import { UnitWithProperty } from '@/lib/services/unitService'
import { getMonthName } from '@/lib/utils'

const invoiceSchema = z.object({
  unit_id: z.string().min(1, 'Unit is required'),
  amount: z.number().min(1, 'Amount must be greater than 0'),
  month: z.number().min(1, 'Month is required').max(12, 'Invalid month'),
  year: z.number().min(2020, 'Year must be 2020 or later').max(2030, 'Year must be 2030 or earlier'),
  due_date: z.string().optional(),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
})

type InvoiceFormData = z.infer<typeof invoiceSchema>

interface InvoiceFormProps {
  invoice?: MaintenanceInvoice
  units: UnitWithProperty[]
  onSubmit: (data: InvoiceFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function InvoiceForm({ invoice, units, onSubmit, onCancel, isLoading = false }: InvoiceFormProps) {
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth() + 1
  const currentYear = currentDate.getFullYear()

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: {
      unit_id: invoice?.unit_id || '',
      amount: invoice?.amount || 0,
      month: invoice?.month || currentMonth,
      year: invoice?.year || currentYear,
      due_date: invoice?.due_date || '',
      description: invoice?.description || '',
    },
  })

  const selectedMonth = watch('month')
  const selectedYear = watch('year')

  const handleFormSubmit = async (data: InvoiceFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  // Generate months array
  const months = Array.from({ length: 12 }, (_, i) => ({
    value: i + 1,
    label: getMonthName(i + 1)
  }))

  // Generate years array (current year ± 2)
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i)

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label htmlFor="unit_id" className="block text-sm font-medium text-gray-700 mb-2">
            Unit <span className="text-red-500">*</span>
          </label>
          <select
            id="unit_id"
            {...register('unit_id')}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            disabled={!!invoice} // Disable unit change when editing
          >
            <option value="">Select a unit</option>
            {units.map((unit) => (
              <option key={unit.id} value={unit.id}>
                {unit.property?.name} - Unit {unit.unit_number}
                {unit.owner_name && ` (${unit.owner_name})`}
              </option>
            ))}
          </select>
          {errors.unit_id && (
            <p className="mt-1 text-sm text-red-600">{errors.unit_id.message}</p>
          )}
        </div>

        <Input
          label="Amount (₹)"
          type="number"
          {...register('amount', { valueAsNumber: true })}
          error={errors.amount?.message}
          placeholder="e.g., 5000"
          min="1"
          step="0.01"
          required
        />

        <div>
          <label htmlFor="month" className="block text-sm font-medium text-gray-700 mb-2">
            Month <span className="text-red-500">*</span>
          </label>
          <select
            id="month"
            {...register('month', { valueAsNumber: true })}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            {months.map((month) => (
              <option key={month.value} value={month.value}>
                {month.label}
              </option>
            ))}
          </select>
          {errors.month && (
            <p className="mt-1 text-sm text-red-600">{errors.month.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-2">
            Year <span className="text-red-500">*</span>
          </label>
          <select
            id="year"
            {...register('year', { valueAsNumber: true })}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            {years.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          {errors.year && (
            <p className="mt-1 text-sm text-red-600">{errors.year.message}</p>
          )}
        </div>

        <Input
          label="Due Date"
          type="date"
          {...register('due_date')}
          error={errors.due_date?.message}
          helperText="Leave empty to set due date to end of month"
        />

        <div className="md:col-span-2">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            id="description"
            {...register('description')}
            rows={3}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder={`Maintenance charges for ${getMonthName(selectedMonth)} ${selectedYear}`}
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={isSubmitting || isLoading}
          disabled={isSubmitting || isLoading}
        >
          {invoice ? 'Update Invoice' : 'Create Invoice'}
        </Button>
      </div>
    </form>
  )
}
