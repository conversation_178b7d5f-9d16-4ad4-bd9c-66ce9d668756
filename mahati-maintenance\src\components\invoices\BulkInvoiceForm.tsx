'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Property } from '@/lib/supabase'
import { getMonthName } from '@/lib/utils'

const bulkInvoiceSchema = z.object({
  property_id: z.string().optional(),
  amount: z.number().min(1, 'Amount must be greater than 0'),
  month: z.number().min(1, 'Month is required').max(12, 'Invalid month'),
  year: z.number().min(2020, 'Year must be 2020 or later').max(2030, 'Year must be 2030 or earlier'),
  due_date: z.string().optional(),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
})

type BulkInvoiceFormData = z.infer<typeof bulkInvoiceSchema>

interface BulkInvoiceFormProps {
  properties: Property[]
  onSubmit: (data: BulkInvoiceFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function BulkInvoiceForm({ properties, onSubmit, onCancel, isLoading = false }: BulkInvoiceFormProps) {
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth() + 1
  const currentYear = currentDate.getFullYear()

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<BulkInvoiceFormData>({
    resolver: zodResolver(bulkInvoiceSchema),
    defaultValues: {
      property_id: '',
      amount: 0,
      month: currentMonth,
      year: currentYear,
      due_date: '',
      description: '',
    },
  })

  const selectedMonth = watch('month')
  const selectedYear = watch('year')
  const selectedProperty = watch('property_id')

  const handleFormSubmit = async (data: BulkInvoiceFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  // Generate months array
  const months = Array.from({ length: 12 }, (_, i) => ({
    value: i + 1,
    label: getMonthName(i + 1)
  }))

  // Generate years array (current year ± 2)
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i)

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Bulk Invoice Generation
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                This will create invoices for all units in the selected property. 
                If no property is selected, invoices will be created for all units across all properties.
              </p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label htmlFor="property_id" className="block text-sm font-medium text-gray-700 mb-2">
              Property (Optional)
            </label>
            <select
              id="property_id"
              {...register('property_id')}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
            <p className="mt-1 text-sm text-gray-500">
              {selectedProperty 
                ? `Invoices will be created for all units in ${properties.find(p => p.id === selectedProperty)?.name}`
                : 'Invoices will be created for all units across all properties'
              }
            </p>
          </div>

          <Input
            label="Amount (₹)"
            type="number"
            {...register('amount', { valueAsNumber: true })}
            error={errors.amount?.message}
            placeholder="e.g., 5000"
            min="1"
            step="0.01"
            required
          />

          <div>
            <label htmlFor="month" className="block text-sm font-medium text-gray-700 mb-2">
              Month <span className="text-red-500">*</span>
            </label>
            <select
              id="month"
              {...register('month', { valueAsNumber: true })}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              {months.map((month) => (
                <option key={month.value} value={month.value}>
                  {month.label}
                </option>
              ))}
            </select>
            {errors.month && (
              <p className="mt-1 text-sm text-red-600">{errors.month.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-2">
              Year <span className="text-red-500">*</span>
            </label>
            <select
              id="year"
              {...register('year', { valueAsNumber: true })}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
            {errors.year && (
              <p className="mt-1 text-sm text-red-600">{errors.year.message}</p>
            )}
          </div>

          <Input
            label="Due Date"
            type="date"
            {...register('due_date')}
            error={errors.due_date?.message}
            helperText="Leave empty to set due date to end of month"
          />

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              placeholder={`Maintenance charges for ${getMonthName(selectedMonth)} ${selectedYear}`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={isSubmitting || isLoading}
            disabled={isSubmitting || isLoading}
          >
            Generate Invoices
          </Button>
        </div>
      </form>
    </div>
  )
}
