import { supabase } from '@/lib/supabase'
import { MaintenanceInvoice, Unit, Property } from '@/lib/supabase'
import { generateInvoiceNumber } from '@/lib/utils'

export interface CreateInvoiceData {
  unit_id: string
  amount: number
  month: number
  year: number
  due_date?: string
  description?: string
}

export interface UpdateInvoiceData {
  amount?: number
  due_date?: string
  description?: string
  status?: 'pending' | 'paid' | 'overdue' | 'cancelled'
}

export interface InvoiceWithUnit extends MaintenanceInvoice {
  unit: Unit & { property: Property }
}

export interface BulkInvoiceData {
  property_id?: string
  amount: number
  month: number
  year: number
  due_date?: string
  description?: string
}

export class InvoiceService {
  static async getAll(): Promise<InvoiceWithUnit[]> {
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .select(`
        *,
        unit:units(
          *,
          property:properties(*)
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch invoices: ${error.message}`)
    }

    return data || []
  }

  static async getByUnitId(unitId: string): Promise<MaintenanceInvoice[]> {
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .select('*')
      .eq('unit_id', unitId)
      .order('year', { ascending: false })
      .order('month', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch invoices for unit: ${error.message}`)
    }

    return data || []
  }

  static async getById(id: string): Promise<InvoiceWithUnit | null> {
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .select(`
        *,
        unit:units(
          *,
          property:properties(*)
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Invoice not found
      }
      throw new Error(`Failed to fetch invoice: ${error.message}`)
    }

    return data
  }

  static async create(invoiceData: CreateInvoiceData): Promise<MaintenanceInvoice> {
    // Check if invoice already exists for this unit, month, and year
    const { data: existingInvoice } = await supabase
      .from('maintenance_invoices')
      .select('id')
      .eq('unit_id', invoiceData.unit_id)
      .eq('month', invoiceData.month)
      .eq('year', invoiceData.year)
      .single()

    if (existingInvoice) {
      throw new Error(`Invoice already exists for this unit in ${invoiceData.month}/${invoiceData.year}`)
    }

    // Generate invoice number
    const invoice_number = generateInvoiceNumber(invoiceData.year, invoiceData.month, invoiceData.unit_id)

    const { data, error } = await supabase
      .from('maintenance_invoices')
      .insert([{
        ...invoiceData,
        invoice_number,
        status: 'pending'
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create invoice: ${error.message}`)
    }

    return data
  }

  static async update(id: string, invoiceData: UpdateInvoiceData): Promise<MaintenanceInvoice> {
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .update(invoiceData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update invoice: ${error.message}`)
    }

    return data
  }

  static async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('maintenance_invoices')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete invoice: ${error.message}`)
    }
  }

  static async bulkCreateForProperty(bulkData: BulkInvoiceData): Promise<MaintenanceInvoice[]> {
    // Get all units for the property (or all units if no property specified)
    let unitsQuery = supabase.from('units').select('id, unit_number')

    if (bulkData.property_id) {
      unitsQuery = unitsQuery.eq('property_id', bulkData.property_id)
    }

    const { data: units, error: unitsError } = await unitsQuery

    if (unitsError) {
      throw new Error(`Failed to fetch units: ${unitsError.message}`)
    }

    if (!units || units.length === 0) {
      throw new Error('No units found for bulk invoice generation')
    }

    // Create invoices for all units
    const invoicesToCreate = units.map(unit => ({
      unit_id: unit.id,
      amount: bulkData.amount,
      month: bulkData.month,
      year: bulkData.year,
      due_date: bulkData.due_date,
      description: bulkData.description,
      invoice_number: generateInvoiceNumber(bulkData.year, bulkData.month, unit.id),
      status: 'pending' as const
    }))

    const { data, error } = await supabase
      .from('maintenance_invoices')
      .insert(invoicesToCreate)
      .select()

    if (error) {
      throw new Error(`Failed to create bulk invoices: ${error.message}`)
    }

    return data || []
  }

  static async getInvoiceStats(): Promise<{
    total: number
    pending: number
    paid: number
    overdue: number
    totalAmount: number
    pendingAmount: number
  }> {
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .select('status, amount')

    if (error) {
      throw new Error(`Failed to fetch invoice stats: ${error.message}`)
    }

    const stats = {
      total: data?.length || 0,
      pending: 0,
      paid: 0,
      overdue: 0,
      totalAmount: 0,
      pendingAmount: 0
    }

    data?.forEach(invoice => {
      stats.totalAmount += invoice.amount
      
      switch (invoice.status) {
        case 'pending':
          stats.pending++
          stats.pendingAmount += invoice.amount
          break
        case 'paid':
          stats.paid++
          break
        case 'overdue':
          stats.overdue++
          stats.pendingAmount += invoice.amount
          break
      }
    })

    return stats
  }

  static async markAsOverdue(): Promise<number> {
    const today = new Date().toISOString().split('T')[0]
    
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .update({ status: 'overdue' })
      .eq('status', 'pending')
      .lt('due_date', today)
      .select('id')

    if (error) {
      throw new Error(`Failed to mark invoices as overdue: ${error.message}`)
    }

    return data?.length || 0
  }

  static async search(query: string): Promise<InvoiceWithUnit[]> {
    const { data, error } = await supabase
      .from('maintenance_invoices')
      .select(`
        *,
        unit:units(
          *,
          property:properties(*)
        )
      `)
      .or(`invoice_number.ilike.%${query}%,description.ilike.%${query}%`)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to search invoices: ${error.message}`)
    }

    return data || []
  }
}
