'use client'

import { useState, useEffect } from 'react'
import { Property } from '@/lib/supabase'
import { PropertyService, CreatePropertyData } from '@/lib/services/propertyService'
import { MainLayout } from '@/components/layout/MainLayout'
import { PropertiesList } from '@/components/properties/PropertiesList'
import { PropertyForm } from '@/components/properties/PropertyForm'
import { Modal, ModalBody, ModalHeader } from '@/components/ui/Modal'
import { toast } from 'react-hot-toast'

type PropertyWithUnitsCount = Property & { units_count: number }

export default function PropertiesPage() {
  const [properties, setProperties] = useState<PropertyWithUnitsCount[]>([])
  const [filteredProperties, setFilteredProperties] = useState<PropertyWithUnitsCount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [modal, setModal] = useState<{
    isOpen: boolean
    type: 'create' | 'edit'
    property?: Property
  }>({
    isOpen: false,
    type: 'create'
  })

  useEffect(() => {
    loadProperties()
  }, [])

  const loadProperties = async () => {
    try {
      setIsLoading(true)
      const data = await PropertyService.getWithUnitsCount()
      setProperties(data)
      setFilteredProperties(data)
    } catch (error) {
      console.error('Failed to load properties:', error)
      toast.error('Failed to load properties')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreate = () => {
    setModal({ isOpen: true, type: 'create' })
  }

  const handleEdit = (property: Property) => {
    setModal({ isOpen: true, type: 'edit', property })
  }

  const handleDelete = async (property: Property) => {
    try {
      await PropertyService.delete(property.id)
      toast.success('Property deleted successfully')
      loadProperties()
    } catch (error) {
      console.error('Failed to delete property:', error)
      toast.error('Failed to delete property')
    }
  }

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setFilteredProperties(properties)
      return
    }

    try {
      const searchResults = await PropertyService.search(query)
      // Add units count to search results
      const resultsWithCount = await Promise.all(
        searchResults.map(async (property) => {
          const fullProperty = properties.find(p => p.id === property.id)
          return {
            ...property,
            units_count: fullProperty?.units_count || 0
          }
        })
      )
      setFilteredProperties(resultsWithCount)
    } catch (error) {
      console.error('Failed to search properties:', error)
      toast.error('Failed to search properties')
    }
  }

  const handleFormSubmit = async (data: CreatePropertyData) => {
    try {
      if (modal.type === 'create') {
        await PropertyService.create(data)
        toast.success('Property created successfully')
      } else if (modal.property) {
        await PropertyService.update(modal.property.id, data)
        toast.success('Property updated successfully')
      }
      
      setModal({ isOpen: false, type: 'create' })
      loadProperties()
    } catch (error) {
      console.error('Failed to save property:', error)
      toast.error('Failed to save property')
      throw error // Re-throw to prevent modal from closing
    }
  }

  const handleModalClose = () => {
    setModal({ isOpen: false, type: 'create' })
  }

  return (
    <MainLayout>
      <PropertiesList
        properties={filteredProperties}
        onEdit={handleEdit}
        onCreate={handleCreate}
        onDelete={handleDelete}
        onSearch={handleSearch}
        isLoading={isLoading}
      />

      {/* Property Form Modal */}
      <Modal
        isOpen={modal.isOpen}
        onClose={handleModalClose}
        title={modal.type === 'create' ? 'Create Property' : 'Edit Property'}
        size="lg"
      >
        <ModalBody>
          <PropertyForm
            property={modal.property}
            onSubmit={handleFormSubmit}
            onCancel={handleModalClose}
          />
        </ModalBody>
      </Modal>
    </MainLayout>
  )
}
