# Mahati Maintenance System

A comprehensive apartment financial transaction management system built with Next.js, TypeScript, and Supabase.

## 🚀 Features

- **Authentication**: Secure user authentication with Supabase Auth
- **Property Management**: Manage apartment buildings and complexes
- **Unit Management**: Track individual apartment units and owners
- **Invoice Generation**: Create and manage maintenance invoices
- **Payment Tracking**: Monitor payment records and status
- **Responsive Design**: Mobile-friendly interface with Tailwind CSS

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Deployment**: Vercel

## 📋 Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- Supabase account (free tier sufficient)
- Vercel account for deployment (optional)

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mahati-maintenance
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env.local` file in the root directory:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   NEXT_PUBLIC_APP_NAME=Mahati Maintenance System
   NEXT_PUBLIC_APP_VERSION=1.0.0
   ```

4. **Database Setup**
   The database schema is already configured in Supabase project:
   - Project ID: icuybtsdyezoiyjjkfof
   - Tables: properties, units, maintenance_invoices, payments
   - Row Level Security (RLS) enabled

5. **Start Development Server**
   ```bash
   npm run dev
   ```
   Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🗄️ Database Schema

### Core Tables
- **properties**: Store apartment building information
- **units**: Store individual apartment unit details
- **maintenance_invoices**: Store monthly maintenance bills
- **payments**: Track payment records

### Security
- Row Level Security (RLS) enabled on all tables
- Admin and owner-based access policies implemented

## 🚀 Deployment

### Vercel Deployment
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
3. Deploy automatically on push to main branch

## 📖 Development Roadmap

### Week 1: Foundation ✅
- [x] Next.js project setup
- [x] Supabase integration
- [x] Authentication system
- [x] Basic layout and navigation
- [x] Deployment configuration

### Week 2: Core UI & Properties (In Progress)
- [ ] Reusable UI components
- [ ] Property CRUD operations
- [ ] Form validation
- [ ] Responsive design

### Week 3: Units & Invoices
- [ ] Unit management system
- [ ] Invoice generation
- [ ] Bulk operations

### Week 4: Payments & Reports
- [ ] Payment tracking
- [ ] Financial reporting
- [ ] Data export

### Week 5: Owner Portal & UX
- [ ] Owner self-service portal
- [ ] Enhanced user experience
- [ ] Accessibility improvements

### Week 6: Production & Documentation
- [ ] Production optimization
- [ ] Comprehensive documentation
- [ ] User acceptance testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
