'use client'

import { useState, useEffect } from 'react'
import { MaintenanceInvoice, Property } from '@/lib/supabase'
import { InvoiceService, CreateInvoiceData, InvoiceWithUnit, BulkInvoiceData } from '@/lib/services/invoiceService'
import { UnitService, UnitWithProperty } from '@/lib/services/unitService'
import { PropertyService } from '@/lib/services/propertyService'
import { MainLayout } from '@/components/layout/MainLayout'
import { InvoicesList } from '@/components/invoices/InvoicesList'
import { InvoiceForm } from '@/components/invoices/InvoiceForm'
import { BulkInvoiceForm } from '@/components/invoices/BulkInvoiceForm'
import { Modal, ModalBody, ModalHeader } from '@/components/ui/Modal'
import { toast } from 'react-hot-toast'

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<InvoiceWithUnit[]>([])
  const [filteredInvoices, setFilteredInvoices] = useState<InvoiceWithUnit[]>([])
  const [units, setUnits] = useState<UnitWithProperty[]>([])
  const [properties, setProperties] = useState<Property[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [modal, setModal] = useState<{
    isOpen: boolean
    type: 'create' | 'edit' | 'bulk'
    invoice?: MaintenanceInvoice
  }>({
    isOpen: false,
    type: 'create'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [invoicesData, unitsData, propertiesData] = await Promise.all([
        InvoiceService.getAll(),
        UnitService.getAll(),
        PropertyService.getAll()
      ])
      setInvoices(invoicesData)
      setFilteredInvoices(invoicesData)
      setUnits(unitsData)
      setProperties(propertiesData)
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('Failed to load invoices and related data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreate = () => {
    setModal({ isOpen: true, type: 'create' })
  }

  const handleBulkCreate = () => {
    setModal({ isOpen: true, type: 'bulk' })
  }

  const handleEdit = (invoice: MaintenanceInvoice) => {
    setModal({ isOpen: true, type: 'edit', invoice })
  }

  const handleDelete = async (invoice: MaintenanceInvoice) => {
    try {
      await InvoiceService.delete(invoice.id)
      toast.success('Invoice deleted successfully')
      loadData()
    } catch (error) {
      console.error('Failed to delete invoice:', error)
      toast.error('Failed to delete invoice')
    }
  }

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setFilteredInvoices(invoices)
      return
    }

    try {
      const searchResults = await InvoiceService.search(query)
      setFilteredInvoices(searchResults)
    } catch (error) {
      console.error('Failed to search invoices:', error)
      toast.error('Failed to search invoices')
    }
  }

  const handleStatusFilter = (status: string) => {
    if (!status) {
      setFilteredInvoices(invoices)
      return
    }

    const filtered = invoices.filter(invoice => invoice.status === status)
    setFilteredInvoices(filtered)
  }

  const handleFormSubmit = async (data: CreateInvoiceData) => {
    try {
      if (modal.type === 'create') {
        await InvoiceService.create(data)
        toast.success('Invoice created successfully')
      } else if (modal.invoice) {
        await InvoiceService.update(modal.invoice.id, data)
        toast.success('Invoice updated successfully')
      }
      
      setModal({ isOpen: false, type: 'create' })
      loadData()
    } catch (error) {
      console.error('Failed to save invoice:', error)
      toast.error('Failed to save invoice')
      throw error // Re-throw to prevent modal from closing
    }
  }

  const handleBulkFormSubmit = async (data: BulkInvoiceData) => {
    try {
      const result = await InvoiceService.bulkCreateForProperty(data)
      toast.success(`${result.length} invoices created successfully`)
      
      setModal({ isOpen: false, type: 'create' })
      loadData()
    } catch (error) {
      console.error('Failed to create bulk invoices:', error)
      toast.error('Failed to create bulk invoices')
      throw error // Re-throw to prevent modal from closing
    }
  }

  const handleModalClose = () => {
    setModal({ isOpen: false, type: 'create' })
  }

  return (
    <MainLayout>
      <InvoicesList
        invoices={filteredInvoices}
        onEdit={handleEdit}
        onCreate={handleCreate}
        onBulkCreate={handleBulkCreate}
        onDelete={handleDelete}
        onSearch={handleSearch}
        onStatusFilter={handleStatusFilter}
        isLoading={isLoading}
      />

      {/* Invoice Form Modal */}
      <Modal
        isOpen={modal.isOpen && (modal.type === 'create' || modal.type === 'edit')}
        onClose={handleModalClose}
        title={modal.type === 'create' ? 'Create Invoice' : 'Edit Invoice'}
        size="lg"
      >
        <ModalBody>
          <InvoiceForm
            invoice={modal.invoice}
            units={units}
            onSubmit={handleFormSubmit}
            onCancel={handleModalClose}
          />
        </ModalBody>
      </Modal>

      {/* Bulk Invoice Form Modal */}
      <Modal
        isOpen={modal.isOpen && modal.type === 'bulk'}
        onClose={handleModalClose}
        title="Bulk Generate Invoices"
        size="lg"
      >
        <ModalBody>
          <BulkInvoiceForm
            properties={properties}
            onSubmit={handleBulkFormSubmit}
            onCancel={handleModalClose}
          />
        </ModalBody>
      </Modal>
    </MainLayout>
  )
}
