{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.absolute {\n  position: absolute;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.static {\n  position: static;\n}\n.sticky {\n  position: sticky;\n}\n.top-1\\/2 {\n  top: calc(1/2 * 100%);\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.mx-auto {\n  margin-inline: auto;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.min-h-full {\n  min-height: 100%;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-full {\n  width: 100%;\n}\n.flex-1 {\n  flex: 1;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.flex-grow {\n  flex-grow: 1;\n}\n.caption-bottom {\n  caption-side: bottom;\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.scale-95 {\n  --tw-scale-x: 95%;\n  --tw-scale-y: 95%;\n  --tw-scale-z: 95%;\n  scale: var(--tw-scale-x) var(--tw-scale-y);\n}\n.scale-100 {\n  --tw-scale-x: 100%;\n  --tw-scale-y: 100%;\n  --tw-scale-z: 100%;\n  scale: var(--tw-scale-x) var(--tw-scale-y);\n}\n.transform {\n  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.appearance-none {\n  appearance: none;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.flex-col {\n  flex-direction: column;\n}\n.items-center {\n  align-items: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.-space-y-px {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(-1px * var(--tw-space-y-reverse));\n    margin-block-end: calc(-1px * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.rounded-none {\n  border-radius: 0;\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-t {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 1px;\n}\n.border-r {\n  border-right-style: var(--tw-border-style);\n  border-right-width: 1px;\n}\n.border-b {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 1px;\n}\n.border-b-2 {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 2px;\n}\n.border-transparent {\n  border-color: transparent;\n}\n.text-center {\n  text-align: center;\n}\n.text-left {\n  text-align: left;\n}\n.text-right {\n  text-align: right;\n}\n.align-middle {\n  vertical-align: middle;\n}\n.leading-none {\n  --tw-leading: 1;\n  line-height: 1;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-0 {\n  opacity: 0%;\n}\n.opacity-25 {\n  opacity: 25%;\n}\n.opacity-75 {\n  opacity: 75%;\n}\n.opacity-100 {\n  opacity: 100%;\n}\n.outline {\n  outline-style: var(--tw-outline-style);\n  outline-width: 1px;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.duration-200 {\n  --tw-duration: 200ms;\n  transition-duration: 200ms;\n}\n.duration-300 {\n  --tw-duration: 300ms;\n  transition-duration: 300ms;\n}\n.peer-disabled\\:cursor-not-allowed {\n  &:is(:where(.peer):disabled ~ *) {\n    cursor: not-allowed;\n  }\n}\n.peer-disabled\\:opacity-70 {\n  &:is(:where(.peer):disabled ~ *) {\n    opacity: 70%;\n  }\n}\n.file\\:border-0 {\n  &::file-selector-button {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n}\n.file\\:bg-transparent {\n  &::file-selector-button {\n    background-color: transparent;\n  }\n}\n.hover\\:underline {\n  &:hover {\n    @media (hover: hover) {\n      text-decoration-line: underline;\n    }\n  }\n}\n.focus\\:z-10 {\n  &:focus {\n    z-index: 10;\n  }\n}\n.focus\\:ring-2 {\n  &:focus {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus\\:ring-offset-2 {\n  &:focus {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n}\n.focus\\:outline-none {\n  &:focus {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.focus-visible\\:ring-2 {\n  &:focus-visible {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus-visible\\:ring-offset-2 {\n  &:focus-visible {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n}\n.focus-visible\\:outline-none {\n  &:focus-visible {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.disabled\\:pointer-events-none {\n  &:disabled {\n    pointer-events: none;\n  }\n}\n.disabled\\:cursor-not-allowed {\n  &:disabled {\n    cursor: not-allowed;\n  }\n}\n.disabled\\:opacity-50 {\n  &:disabled {\n    opacity: 50%;\n  }\n}\n.\\[\\&_tr\\]\\:border-b {\n  & tr {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n}\n.\\[\\&_tr\\:last-child\\]\\:border-0 {\n  & tr:last-child {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n}\n.\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n  &>tr {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n}\nbody {\n  background-color: white;\n  color: #111827;\n  font-family: Inter, sans-serif;\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-border-style: solid;\n      --tw-leading: initial;\n      --tw-outline-style: solid;\n      --tw-duration: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAieE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjeJ;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIE;;;;;;AAMF;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAKE;;;;AAKA;;;;AAKA;;;;;AAAA;;;;;AAMA;;;;AAAA;;;;AAME;EAAuB;;;;;;AAMzB;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAOE;;;;;AAMJ;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA"}}]}