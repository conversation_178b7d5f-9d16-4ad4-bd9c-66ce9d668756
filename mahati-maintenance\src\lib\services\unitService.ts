import { supabase } from '@/lib/supabase'
import { Unit, Property } from '@/lib/supabase'

export interface CreateUnitData {
  property_id: string
  unit_number: string
  floor?: number
  area?: number
  owner_name?: string
  owner_email?: string
  owner_phone?: string
}

export interface UpdateUnitData {
  unit_number?: string
  floor?: number
  area?: number
  owner_name?: string
  owner_email?: string
  owner_phone?: string
}

export interface UnitWithProperty extends Unit {
  property: Property
}

export class UnitService {
  static async getAll(): Promise<UnitWithProperty[]> {
    const { data, error } = await supabase
      .from('units')
      .select(`
        *,
        property:properties(*)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch units: ${error.message}`)
    }

    return data || []
  }

  static async getByPropertyId(propertyId: string): Promise<Unit[]> {
    const { data, error } = await supabase
      .from('units')
      .select('*')
      .eq('property_id', propertyId)
      .order('unit_number', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch units for property: ${error.message}`)
    }

    return data || []
  }

  static async getById(id: string): Promise<UnitWithProperty | null> {
    const { data, error } = await supabase
      .from('units')
      .select(`
        *,
        property:properties(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Unit not found
      }
      throw new Error(`Failed to fetch unit: ${error.message}`)
    }

    return data
  }

  static async create(unitData: CreateUnitData): Promise<Unit> {
    // Check if unit number already exists in the property
    const { data: existingUnit } = await supabase
      .from('units')
      .select('id')
      .eq('property_id', unitData.property_id)
      .eq('unit_number', unitData.unit_number)
      .single()

    if (existingUnit) {
      throw new Error(`Unit number ${unitData.unit_number} already exists in this property`)
    }

    const { data, error } = await supabase
      .from('units')
      .insert([unitData])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create unit: ${error.message}`)
    }

    return data
  }

  static async update(id: string, unitData: UpdateUnitData): Promise<Unit> {
    const { data, error } = await supabase
      .from('units')
      .update(unitData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update unit: ${error.message}`)
    }

    return data
  }

  static async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('units')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete unit: ${error.message}`)
    }
  }

  static async search(query: string): Promise<UnitWithProperty[]> {
    const { data, error } = await supabase
      .from('units')
      .select(`
        *,
        property:properties(*)
      `)
      .or(`unit_number.ilike.%${query}%,owner_name.ilike.%${query}%,owner_email.ilike.%${query}%`)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to search units: ${error.message}`)
    }

    return data || []
  }

  static async bulkCreate(units: CreateUnitData[]): Promise<Unit[]> {
    const { data, error } = await supabase
      .from('units')
      .insert(units)
      .select()

    if (error) {
      throw new Error(`Failed to create units: ${error.message}`)
    }

    return data || []
  }

  static async getUnitsWithInvoiceStats(): Promise<(UnitWithProperty & { 
    pending_invoices: number
    total_outstanding: number 
  })[]> {
    const { data, error } = await supabase
      .from('units')
      .select(`
        *,
        property:properties(*),
        maintenance_invoices!inner(
          id,
          amount,
          status
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch units with invoice stats: ${error.message}`)
    }

    return (data || []).map(unit => ({
      ...unit,
      pending_invoices: unit.maintenance_invoices?.filter((inv: any) => inv.status === 'pending').length || 0,
      total_outstanding: unit.maintenance_invoices?.filter((inv: any) => inv.status === 'pending')
        .reduce((sum: number, inv: any) => sum + inv.amount, 0) || 0
    }))
  }
}
