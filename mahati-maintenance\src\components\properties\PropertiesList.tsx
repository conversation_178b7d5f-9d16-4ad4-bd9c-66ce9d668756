'use client'

import { useState } from 'react'
import { Property } from '@/lib/supabase'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import { Modal, ModalBody, ModalFooter, ModalHeader } from '@/components/ui/Modal'
import { formatDateShort } from '@/lib/utils'
import {
  PencilIcon,
  TrashIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  BuildingOffice2Icon,
} from '@heroicons/react/24/outline'

interface PropertiesListProps {
  properties: (Property & { units_count?: number })[]
  onEdit: (property: Property) => void
  onCreate: () => void
  onDelete: (property: Property) => void
  onSearch: (query: string) => void
  isLoading?: boolean
}

export function PropertiesList({
  properties,
  onEdit,
  onCreate,
  onDelete,
  onSearch,
  isLoading = false
}: PropertiesListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; property: Property | null }>({
    isOpen: false,
    property: null
  })

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch(query)
  }

  const handleDeleteClick = (property: Property) => {
    setDeleteModal({ isOpen: true, property })
  }

  const handleDeleteConfirm = () => {
    if (deleteModal.property) {
      onDelete(deleteModal.property)
      setDeleteModal({ isOpen: false, property: null })
    }
  }

  const handleDeleteCancel = () => {
    setDeleteModal({ isOpen: false, property: null })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Properties</h1>
          <p className="text-gray-600">Manage apartment buildings and complexes</p>
        </div>
        <Button onClick={onCreate} className="flex items-center">
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Property
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search properties by name or address..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Properties Table */}
      <Card>
        <CardHeader>
          <CardTitle>Properties ({properties.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {properties.length === 0 ? (
            <div className="text-center py-12">
              <BuildingOffice2Icon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No properties</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first property.
              </p>
              <div className="mt-6">
                <Button onClick={onCreate}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Property
                </Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead>Units</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {properties.map((property) => (
                  <TableRow key={property.id}>
                    <TableCell className="font-medium">{property.name}</TableCell>
                    <TableCell className="max-w-xs truncate">{property.address}</TableCell>
                    <TableCell>{property.units_count || 0}</TableCell>
                    <TableCell>{formatDateShort(property.created_at)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(property)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteClick(property)}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        title="Delete Property"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            Are you sure you want to delete "{deleteModal.property?.name}"? This action cannot be undone.
            All associated units and data will also be deleted.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button variant="outline" onClick={handleDeleteCancel}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDeleteConfirm}>
            Delete Property
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  )
}
