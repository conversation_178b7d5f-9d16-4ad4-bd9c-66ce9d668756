'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Property } from '@/lib/supabase'

const propertySchema = z.object({
  name: z.string().min(1, 'Property name is required').max(100, 'Property name must be less than 100 characters'),
  address: z.string().min(1, 'Address is required').max(500, 'Address must be less than 500 characters'),
})

type PropertyFormData = z.infer<typeof propertySchema>

interface PropertyFormProps {
  property?: Property
  onSubmit: (data: PropertyFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function PropertyForm({ property, onSubmit, onCancel, isLoading = false }: PropertyFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<PropertyFormData>({
    resolver: zod<PERSON><PERSON>olver(propertySchema),
    defaultValues: {
      name: property?.name || '',
      address: property?.address || '',
    },
  })

  const handleFormSubmit = async (data: PropertyFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <Input
          label="Property Name"
          {...register('name')}
          error={errors.name?.message}
          placeholder="Enter property name (e.g., Sunrise Apartments)"
          required
        />

        <div>
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
            Address <span className="text-red-500">*</span>
          </label>
          <textarea
            id="address"
            {...register('address')}
            rows={3}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="Enter complete address including city, state, and postal code"
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={isSubmitting || isLoading}
          disabled={isSubmitting || isLoading}
        >
          {property ? 'Update Property' : 'Create Property'}
        </Button>
      </div>
    </form>
  )
}
