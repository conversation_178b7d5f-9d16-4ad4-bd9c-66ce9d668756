'use client'

import { useState } from 'react'
import { Unit, Property } from '@/lib/supabase'
import { UnitWithProperty } from '@/lib/services/unitService'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import { Modal, ModalBody, ModalFooter, ModalHeader } from '@/components/ui/Modal'
import { formatDateShort } from '@/lib/utils'
import {
  PencilIcon,
  TrashIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  BuildingOffice2Icon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
} from '@heroicons/react/24/outline'

interface UnitsListProps {
  units: UnitWithProperty[]
  properties: Property[]
  onEdit: (unit: Unit) => void
  onCreate: () => void
  onDelete: (unit: Unit) => void
  onSearch: (query: string) => void
  onPropertyFilter: (propertyId: string) => void
  isLoading?: boolean
}

export function UnitsList({
  units,
  properties,
  onEdit,
  onCreate,
  onDelete,
  onSearch,
  onPropertyFilter,
  isLoading = false
}: UnitsListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProperty, setSelectedProperty] = useState('')
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; unit: Unit | null }>({
    isOpen: false,
    unit: null
  })

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch(query)
  }

  const handlePropertyFilter = (propertyId: string) => {
    setSelectedProperty(propertyId)
    onPropertyFilter(propertyId)
  }

  const handleDeleteClick = (unit: Unit) => {
    setDeleteModal({ isOpen: true, unit })
  }

  const handleDeleteConfirm = () => {
    if (deleteModal.unit) {
      onDelete(deleteModal.unit)
      setDeleteModal({ isOpen: false, unit: null })
    }
  }

  const handleDeleteCancel = () => {
    setDeleteModal({ isOpen: false, unit: null })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Units</h1>
          <p className="text-gray-600">Manage individual apartment units and owners</p>
        </div>
        <Button onClick={onCreate} className="flex items-center">
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Unit
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search units by number, owner name, or email..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedProperty}
              onChange={(e) => handlePropertyFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">All Properties</option>
              {properties.map((property) => (
                <option key={property.id} value={property.id}>
                  {property.name}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Units Table */}
      <Card>
        <CardHeader>
          <CardTitle>Units ({units.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {units.length === 0 ? (
            <div className="text-center py-12">
              <BuildingOffice2Icon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No units</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first unit.
              </p>
              <div className="mt-6">
                <Button onClick={onCreate}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Unit
                </Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Unit</TableHead>
                  <TableHead>Property</TableHead>
                  <TableHead>Floor</TableHead>
                  <TableHead>Area</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {units.map((unit) => (
                  <TableRow key={unit.id}>
                    <TableCell className="font-medium">{unit.unit_number}</TableCell>
                    <TableCell>{unit.property?.name}</TableCell>
                    <TableCell>{unit.floor || '-'}</TableCell>
                    <TableCell>{unit.area ? `${unit.area} sq ft` : '-'}</TableCell>
                    <TableCell>
                      {unit.owner_name ? (
                        <div className="flex items-center">
                          <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                          {unit.owner_name}
                        </div>
                      ) : (
                        <span className="text-gray-400">No owner</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {unit.owner_email && (
                          <div className="flex items-center text-sm">
                            <EnvelopeIcon className="h-3 w-3 text-gray-400 mr-1" />
                            {unit.owner_email}
                          </div>
                        )}
                        {unit.owner_phone && (
                          <div className="flex items-center text-sm">
                            <PhoneIcon className="h-3 w-3 text-gray-400 mr-1" />
                            {unit.owner_phone}
                          </div>
                        )}
                        {!unit.owner_email && !unit.owner_phone && (
                          <span className="text-gray-400 text-sm">No contact</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{formatDateShort(unit.created_at)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(unit)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteClick(unit)}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        title="Delete Unit"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            Are you sure you want to delete unit "{deleteModal.unit?.unit_number}"? This action cannot be undone.
            All associated invoices and payment data will also be deleted.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button variant="outline" onClick={handleDeleteCancel}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDeleteConfirm}>
            Delete Unit
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  )
}
